from langsmith import Client
import os
from datetime import datetime

# Set your API key in environment
os.environ["LANGCHAIN_API_KEY"] = "your_key_here"

client = Client()
runs = client.list_runs(
    project_name="SALESPULSE",
    start_time=datetime(2025, 7, 28),
    end_time=datetime.now()
)

print(f"Total runs found: {len(runs)}")

# Display the runs
for run in runs:
    print(run.id, run.start_time, run.status)   
    